{"name": "n8n-nodes-anywebsites", "version": "1.1.0", "description": "n8n node for AnyWebsites HTML hosting service", "keywords": ["n8n-community-node-package", "n8n", "anywebsites", "html", "hosting", "website"], "license": "MIT", "homepage": "https://github.com/anywebsites/n8n-nodes-anywebsites", "author": {"name": "AnyWebsites Team", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/anywebsites/n8n-nodes-anywebsites.git"}, "engines": {"node": ">=18.10"}, "main": "dist/index.js", "scripts": {"build": "tsc && gulp build:icons", "dev": "tsc --watch", "format": "prettier nodes credentials --write", "lint": "eslint \"nodes/**/*.ts\" \"credentials/**/*.ts\" --ignore-pattern \"**/*.test.ts\"", "lintfix": "eslint \"nodes/**/*.ts\" \"credentials/**/*.ts\" --ignore-pattern \"**/*.test.ts\" --fix", "prepublishOnly": "npm run build && npm run lint && npm test", "test": "jest"}, "files": ["dist", "README.md", "LICENSE", "CHANGELOG.md"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/AnyWebsitesApi.credentials.js"], "nodes": ["dist/nodes/AnyWebsites/AnyWebsites.node.js"]}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^18.16.16", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.45.0", "eslint-plugin-n8n-nodes-base": "^1.11.0", "gulp": "^4.0.2", "jest": "^29.6.2", "n8n-workflow": "^1.2.0", "prettier": "^2.7.1", "ts-jest": "^29.1.1", "typescript": "^4.8.4"}, "peerDependencies": {"n8n-workflow": "*"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/nodes", "<rootDir>/credentials"]}}