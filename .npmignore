# Source files
nodes/
credentials/
*.ts
!dist/**/*.d.ts

# Development files
.eslintrc.js
.prettierrc
tsconfig.json
gulpfile.js
jest.config.js

# Test files
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js
test/
tests/
__tests__/

# Development dependencies
node_modules/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
.nyc_output

# Cache
.eslintcache
.cache

# Temporary files
tmp/
temp/

# Git files
.git/
.gitignore

# Documentation source (keep only essential docs)
CONTRIBUTING.md
EXAMPLES.md
PROJECT_SUMMARY.md

# Build artifacts that shouldn't be published
*.tsbuildinfo
