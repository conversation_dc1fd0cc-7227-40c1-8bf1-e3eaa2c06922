{"compilerOptions": {"module": "commonjs", "target": "es2019", "lib": ["es2019"], "declaration": true, "outDir": "./dist", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["credentials/**/*", "nodes/**/*", "index.ts"], "exclude": ["dist", "node_modules", "**/*.test.ts"]}