# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tgz

# Development files
src/
*.ts
!*.d.ts
tsconfig.json
jest.config.js
.eslintrc.js

# Git
.git/
.gitignore

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Test files
**/*.test.ts
**/*.test.js
test/
coverage/

# Documentation
docs/
*.md
!README.md

# CI/CD
.github/
.gitlab-ci.yml

# Local development
.env
.env.local
.env.development
.env.test
.env.production

# Temporary files
tmp/
temp/
